package com.botong.controller;

import com.botong.services.service.BtoDeviceEnginerService;
import com.botong.services.service.BtoDeviceModulesService;
import com.botong.services.service.BtoStationEnginerService;
import entityDTO.BtoDeviceBindingDTO;
import entityDTO.BtoDeviceEnginerDTO;
import entityDTO.DeviceDTO;
import entityDTO.UpdateDeviceFormDTO;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import utils.AjaxResult;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("btoDeviceEnginer")
@Api(tags = "工程师-逆变器")
public class BtoDeviceEnginerController {
    @Autowired
    private BtoDeviceEnginerService btoDeviceEnginerService;
    @Autowired
    private BtoDeviceModulesService btoDeviceModulesService;
    @Autowired
    private BtoStationEnginerService btoStationEnginerService;

    @ApiOperation("绑定逆变器和光伏板")
    @PostMapping("setDeviceEnginerAndModules")
    public AjaxResult setDeviceEnginer(@RequestBody BtoDeviceBindingDTO btoDeviceBindingDTO) {
        Integer flag1 = btoDeviceEnginerService.setDevice(btoDeviceBindingDTO);
        Integer flag2 = btoDeviceModulesService.addDeviceModules(btoDeviceBindingDTO);
        if (flag1 == 1 && flag2 == 1) {
            return AjaxResult.success("注册成功");
        } else {
            return AjaxResult.error("注册失败,可能是该电站已存在");
        }
    }

    @ApiOperation("根据电站名或手机号码查询逆变器SN和IMEI")
    @GetMapping("/getDeviceCode")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "value", value = "参数值", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "type", value = "参数类型：[nameStr:名字，phone:手机号码]", dataType = "String", paramType = "query")
    })
    public AjaxResult getDeviceCode(String value, String type) {
        List<HashMap<String, String>> deviceCode = btoDeviceEnginerService.getDeviceCode(value, type);
        return AjaxResult.success(deviceCode);
    }

    @ApiOperation("更新逆变器")
    @PutMapping("/updateDevice")
    public AjaxResult updateDevice(@RequestBody UpdateDeviceFormDTO updateDeviceFormDTO ) throws IllegalAccessException {
        String result = btoDeviceEnginerService.updateDevice(updateDeviceFormDTO);
        return AjaxResult.success(result);
    }


    @ApiOperation("根据电站名查询电站id")
    @PostMapping("getStationIDByName")
    public AjaxResult getStationIDByName(String name) {
        String plantId = btoDeviceEnginerService.getStationUIDByName(name);
        if (plantId == null || plantId.equals("")) {
            return AjaxResult.error("该电站不存在");
        }
        return AjaxResult.success("查询成功", plantId);
    }

    @ApiOperation("根据逆变器SN查询该逆变器是否已存在")
    @PostMapping("getDeviceBySN")
    public AjaxResult getDeviceBySN(String sn) {
        List<BtoDeviceEnginerDTO> deviceBySN = btoDeviceEnginerService.getDeviceBySN(sn);
        if (deviceBySN.isEmpty()) {
            return AjaxResult.success("允许插入");
        } else {
            return AjaxResult.error("该逆变器已被绑定，请更换其他逆变器");
        }

    }

}
