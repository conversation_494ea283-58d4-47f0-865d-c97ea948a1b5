package com.botong.controller;

import com.botong.services.service.AlarmAnalysisService;
import com.botong.services.service.userEnginerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import utils.AjaxResult;

@RestController
@RequestMapping("AlarmAnalysis")
@Api(tags = "工程师接口——用户模块")
public class userEnginerController {

    @Autowired
    private com.botong.services.service.userEnginerService userEnginerService;

    @ApiOperation("注册用户")
    @PostMapping("registeredUser")
    public AjaxResult registeredUser(String cUserName,String cUsertel,String cUserEmail,String engnierId,Integer special) {
        if (userEnginerService.registeredUser(cUserName, cUsertel, cUserEmail,engnierId,special) == 1) {
            return AjaxResult.success("操作成功","1");
        } else {
            return AjaxResult.success("创建失败","0");
        }
    }
    @ApiOperation("查询用户id")
    @GetMapping("getid")
    public AjaxResult getid(String cUserName){
        return AjaxResult.success("操作成功",userEnginerService.selectUid(cUserName));
    }

    @ApiOperation("查询用户的电话号码_第二个版本优化，跳过注册用户——河源")
    @GetMapping("getUseroPhone")
    public AjaxResult getUseroPhone(String cUserPhone){
        return AjaxResult.success("操作成功",userEnginerService.getUseroPhone(cUserPhone));
    }

    @ApiOperation("项目专项")
    @GetMapping("QueryAreaUsers")
    public AjaxResult QueryAreaUsers(){
        return AjaxResult.success("查询成功",userEnginerService.QueryAreaUsers());
    }

    @ApiOperation("绑定智慧运维器")
    @GetMapping("bindSmartOperator")
    public AjaxResult bindSmartOperator(String stationName,String SmartOperatorName,Integer num){
        if (userEnginerService.bindSmartOperator(stationName,SmartOperatorName,num) == 1) {
            return AjaxResult.success("操作成功","1");
        } else {
            return AjaxResult.success("创建失败","0");
        }
    }


    @ApiOperation("修改密码")
    @PostMapping("updatePassword")
    public AjaxResult updatePassword(String userUid,String password){
        if (userEnginerService.updatePassword(userUid,password)==1){
            return AjaxResult.success("修改成功",userEnginerService.updatePassword(userUid,password));
        }else {
            return AjaxResult.success("修改失败，用户可能不存在！",userEnginerService.updatePassword(userUid,password));
        }
    }

    @ApiOperation("所属公司id")
    @PostMapping("SelectcompanyId")
    public AjaxResult SelectcompanyId(){
        return AjaxResult.success("查询成功",userEnginerService.SelectcompanyId());
    }

    @ApiOperation("查询所有用户_测试——废弃")
    @GetMapping("SelUserAll")
    public AjaxResult SelUserAll(){
        return AjaxResult.success("查询成功",userEnginerService.SelUserAll());
    }

    @ApiOperation("查重用户名")
    @GetMapping("CheckUsername")
    public AjaxResult CheckUsername(String userName){
            return AjaxResult.success("查询成功",userEnginerService.CheckUsername(userName));
    }

    @ApiOperation("版本查看")
    @GetMapping("versionView")
    public AjaxResult versionView(){
        return AjaxResult.success("版本查询成功",userEnginerService.versionView());
    }


    @ApiOperation("逆变器型号")
    @GetMapping("InverterModel")
    public AjaxResult InverterModel(){
        return AjaxResult.success("查询成功",userEnginerService.InverterModel());
    }

    @ApiOperation("读取文件名称")
    @GetMapping("readFileName")
    public AjaxResult readFileName(){
        if (userEnginerService.readFileName()==null){
            return AjaxResult.success("查询失败",0);
        }else {
            return AjaxResult.success("查询成功",userEnginerService.readFileName());
        }
    }


    @ApiOperation("查询IMEI码")
    @GetMapping("QueryIMEIcode")
    public AjaxResult QueryIMEIcode(String IMEI){
        if (userEnginerService.QueryIMEIcode(IMEI)==1){
            return AjaxResult.success("IMEI码存在",1);
        }else {
            return AjaxResult.success("IMEI码不存在！",0);
        }
    }

}
