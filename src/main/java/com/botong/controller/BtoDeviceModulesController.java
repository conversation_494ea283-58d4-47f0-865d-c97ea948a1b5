package com.botong.controller;

import com.botong.services.service.BtoDeviceModulesService;
import com.botong.services.service.BtoStationEnginerService;
import entity.BtoDeviceModules;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import utils.AjaxResult;


/**
 * 光伏组件
 *
 * <AUTHOR> @date 2022-09-20 15:36:30
 */
@RestController
@RequestMapping("/deviceModules")
@Api(tags = "光伏组件")
public class BtoDeviceModulesController {

    @Autowired
    private BtoDeviceModulesService btoDeviceModulesService;

    @Autowired
    private BtoStationEnginerService btoStationEnginerService;

//    @ApiOperation("光伏板添加")
//    @PutMapping("/addDeviceModules")
//    public AjaxResult addDeviceModules(@RequestBody BtoDeviceModules deviceModules, String stationName) {
//        return AjaxResult.success("操作成功", btoDeviceModulesService.addDeviceModules(deviceModules, btoStationEnginerService.selectPlantUidByName(stationName)));
//    }

}
