//package com.botong.controller;
//
//import com.botong.services.service.AlarmAnalysisService;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import net.dongliu.apk.parser.ApkFile;
//import net.dongliu.apk.parser.bean.ApkMeta;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//import utils.AjaxResult;
//
//import java.io.File;
//
//@RestController
//@RequestMapping("AlarmAnalysis")
//@Api(tags = "工程师app接口")
//public class hy_test {
//
//    @Autowired
//    private AlarmAnalysisService service;
//
//    @ApiOperation("实时报警——列表表格——测试")
//    @GetMapping("AlarmAnalysisListForm")
//    public AjaxResult AlarmAnalysisListForm(Integer page, Integer pageSize){
//        return AjaxResult.success("操作成功",service.AlarmAnalysisListForm(page,pageSize));
//    }
//
//
//
//}